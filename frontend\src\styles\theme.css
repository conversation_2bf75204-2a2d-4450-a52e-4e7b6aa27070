/* CSS Variables for Centralized Theme Management */

:root {
  /* Light Theme Colors */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #dbeafe;
  
  --color-secondary: #6b7280;
  --color-secondary-hover: #4b5563;
  
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;
  
  /* Border Colors */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: #3b82f6;
  
  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Dark Theme - Automatically applied based on system preference */
@media (prefers-color-scheme: dark) {
  :root {
    /* Background Colors */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-overlay: rgba(0, 0, 0, 0.75);
    
    /* Text Colors */
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --text-inverse: #111827;
    
    /* Border Colors */
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --border-focus: #60a5fa;
    
    /* Adjust primary colors for better contrast in dark mode */
    --color-primary: #60a5fa;
    --color-primary-hover: #3b82f6;
    --color-primary-light: #1e3a8a;
    
    /* Adjust other colors for dark mode */
    --color-success: #34d399;
    --color-success-light: #064e3b;
    
    --color-error: #f87171;
    --color-error-light: #7f1d1d;
    
    --color-warning: #fbbf24;
    --color-warning-light: #78350f;
  }
}

/* Base styles for smooth transitions */
* {
  transition: background-color var(--transition-normal), 
              border-color var(--transition-normal), 
              color var(--transition-normal);
}

/* Utility classes for common patterns */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-border {
  border-color: var(--border-primary);
}

.theme-shadow {
  box-shadow: var(--shadow-md);
}
