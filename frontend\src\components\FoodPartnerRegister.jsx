import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const FoodPartnerRegister = () => {
  const [formData, setFormData] = useState({
    restaurantName: '',
    ownerFirstName: '',
    ownerLastName: '',
    email: '',
    phone: '',
    businessPhone: '',
    password: '',
    confirmPassword: '',
    restaurantAddress: '',
    city: '',
    zipCode: '',
    cuisineType: '',
    businessLicense: '',
    description: '',
    agreeToTerms: false
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Form submission logic will be implemented later
    console.log('Food partner registration form submitted:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1 className="auth-title">Partner With Us</h1>
          <p className="auth-subtitle">
            Join our platform and reach more customers with your delicious food
          </p>
        </div>

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="restaurantName" className="form-label">
              Restaurant Name
            </label>
            <input
              type="text"
              id="restaurantName"
              name="restaurantName"
              value={formData.restaurantName}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your restaurant name"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="ownerFirstName" className="form-label">
                Owner First Name
              </label>
              <input
                type="text"
                id="ownerFirstName"
                name="ownerFirstName"
                value={formData.ownerFirstName}
                onChange={handleInputChange}
                className="form-input"
                placeholder="First name"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="ownerLastName" className="form-label">
                Owner Last Name
              </label>
              <input
                type="text"
                id="ownerLastName"
                name="ownerLastName"
                value={formData.ownerLastName}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Last name"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your email address"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone" className="form-label">
                Personal Phone
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Your phone number"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="businessPhone" className="form-label">
                Business Phone
              </label>
              <input
                type="tel"
                id="businessPhone"
                name="businessPhone"
                value={formData.businessPhone}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Restaurant phone"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="restaurantAddress" className="form-label">
              Restaurant Address
            </label>
            <input
              type="text"
              id="restaurantAddress"
              name="restaurantAddress"
              value={formData.restaurantAddress}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter restaurant address"
              required
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="city" className="form-label">
                City
              </label>
              <input
                type="text"
                id="city"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Enter city"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="zipCode" className="form-label">
                ZIP Code
              </label>
              <input
                type="text"
                id="zipCode"
                name="zipCode"
                value={formData.zipCode}
                onChange={handleInputChange}
                className="form-input"
                placeholder="Enter ZIP code"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="cuisineType" className="form-label">
              Cuisine Type
            </label>
            <select
              id="cuisineType"
              name="cuisineType"
              value={formData.cuisineType}
              onChange={handleInputChange}
              className="form-select"
              required
            >
              <option value="">Select cuisine type</option>
              <option value="italian">Italian</option>
              <option value="chinese">Chinese</option>
              <option value="indian">Indian</option>
              <option value="mexican">Mexican</option>
              <option value="american">American</option>
              <option value="thai">Thai</option>
              <option value="japanese">Japanese</option>
              <option value="mediterranean">Mediterranean</option>
              <option value="fast-food">Fast Food</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="businessLicense" className="form-label">
              Business License Number
            </label>
            <input
              type="text"
              id="businessLicense"
              name="businessLicense"
              value={formData.businessLicense}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter business license number"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description" className="form-label">
              Restaurant Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Tell us about your restaurant..."
              rows="3"
              style={{ resize: 'vertical', minHeight: '80px' }}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Create a strong password"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="confirmPassword" className="form-label">
              Confirm Password
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Confirm your password"
              required
            />
          </div>

          <div className="form-checkbox-group">
            <input
              type="checkbox"
              id="agreeToTerms"
              name="agreeToTerms"
              checked={formData.agreeToTerms}
              onChange={handleInputChange}
              className="form-checkbox"
              required
            />
            <label htmlFor="agreeToTerms" className="form-checkbox-label">
              I agree to the Partner Terms of Service and Privacy Policy
            </label>
          </div>

          <button
            type="submit"
            className="form-button"
            disabled={!formData.agreeToTerms}
          >
            Register as Partner
          </button>
        </form>

        <div className="auth-link">
          <span className="auth-link-text">
            Already have a partner account?{' '}
            <Link to="/food-partner/login" className="auth-link-button">
              Sign in
            </Link>
          </span>
        </div>

        <div className="auth-divider">
          <span className="auth-divider-text">or</span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            Looking to order food?{' '}
            <Link to="/user/register" className="auth-link-button">
              Create User Account
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
};

export default FoodPartnerRegister;
