import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const FoodPartnerLogin = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Form submission logic will be implemented later
    console.log('Food partner login form submitted:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1 className="auth-title">Partner Portal</h1>
          <p className="auth-subtitle">
            Sign in to manage your restaurant and orders
          </p>
        </div>

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your email address"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="form-checkbox-group">
            <input
              type="checkbox"
              id="rememberMe"
              name="rememberMe"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              className="form-checkbox"
            />
            <label htmlFor="rememberMe" className="form-checkbox-label">
              Keep me signed in
            </label>
          </div>

          <button type="submit" className="form-button">
            Sign In to Dashboard
          </button>
        </form>

        <div className="auth-link">
          <Link to="/food-partner/forgot-password" className="auth-link-button">
            Forgot your password?
          </Link>
        </div>

        <div className="auth-divider">
          <span className="auth-divider-text">or</span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            New to our platform?{' '}
            <Link to="/food-partner/register" className="auth-link-button">
              Register as Partner
            </Link>
          </span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            Are you a customer?{' '}
            <Link to="/user/login" className="auth-link-button">
              Customer Login
            </Link>
          </span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            Need help?{' '}
            <Link to="/partner-support" className="auth-link-button">
              Contact Support
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
};

export default FoodPartnerLogin;
