import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const UserLogin = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Form submission logic will be implemented later
    console.log('User login form submitted:', formData);
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1 className="auth-title">Welcome Back</h1>
          <p className="auth-subtitle">
            Sign in to your account to continue exploring delicious food
          </p>
        </div>

        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your email address"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className="form-input"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="form-checkbox-group">
            <input
              type="checkbox"
              id="rememberMe"
              name="rememberMe"
              checked={formData.rememberMe}
              onChange={handleInputChange}
              className="form-checkbox"
            />
            <label htmlFor="rememberMe" className="form-checkbox-label">
              Remember me
            </label>
          </div>

          <button type="submit" className="form-button">
            Sign In
          </button>
        </form>

        <div className="auth-link">
          <Link to="/forgot-password" className="auth-link-button">
            Forgot your password?
          </Link>
        </div>

        <div className="auth-divider">
          <span className="auth-divider-text">or</span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            Don't have an account?{' '}
            <Link to="/user/register" className="auth-link-button">
              Create account
            </Link>
          </span>
        </div>

        <div className="auth-link">
          <span className="auth-link-text">
            Are you a restaurant owner?{' '}
            <Link to="/food-partner/login" className="auth-link-button">
              Partner Login
            </Link>
          </span>
        </div>
      </div>
    </div>
  );
};

export default UserLogin;
